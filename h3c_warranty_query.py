#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
H3C设备维保信息查询脚本
从Excel文件中读取设备信息，查询H3C官网维保状态，并保存到新的sheet
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime
import re
from urllib.parse import quote

class H3CWarrantyQuery:
    def __init__(self):
        self.base_url = "https://es.h3c.com/entitlement/query"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://es.h3c.com/entitlement/',
            'Origin': 'https://es.h3c.com'
        })
    
    def query_warranty(self, serial_number, product_number=""):
        """
        查询单个设备的维保信息
        
        Args:
            serial_number (str): 设备序列号
            product_number (str): 产品号（可选）
            
        Returns:
            dict: 维保信息字典
        """
        try:
            params = {
                'serialNumber': serial_number,
                'productNumber': product_number,
                'language': 'CN',
                'captchaVerification': ''
            }
            
            print(f"正在查询序列号: {serial_number}")
            response = self.session.get(self.base_url, params=params, timeout=30)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    return self.parse_warranty_data(data, serial_number)
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析HTML
                    return self.parse_html_response(response.text, serial_number)
            else:
                print(f"查询失败，状态码: {response.status_code}")
                return self.create_error_result(serial_number, f"HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"网络请求错误: {e}")
            return self.create_error_result(serial_number, f"网络错误: {str(e)}")
        except Exception as e:
            print(f"查询出错: {e}")
            return self.create_error_result(serial_number, f"查询错误: {str(e)}")
    
    def parse_warranty_data(self, data, serial_number):
        """
        解析维保数据
        """
        result = {
            'serial_number': serial_number,
            'product_description': '',
            'product_line': '',
            'product_line_desc': '',
            'region': '',
            'warranty_records': [],
            'query_status': '成功',
            'query_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            if isinstance(data, dict) and data.get('code') == 200:
                es_data = data.get('data', {}).get('es', [])

                if es_data and len(es_data) > 0:
                    entitlement = es_data[0].get('combinedUnitEntitlement', {})
                    oos = entitlement.get('oos', {})

                    # 提取产品基本信息
                    product = oos.get('product', {})
                    result['product_description'] = product.get('productDesc', '')
                    result['product_line'] = product.get('productLineCode', '')
                    result['product_line_desc'] = product.get('productLineDesc', '')
                    result['region'] = oos.get('shipToCountry', '')

                    # 提取维保记录 - 从warrantyList
                    warranty_list = entitlement.get('warrantyList', [])
                    for warranty in warranty_list:
                        offer_list = warranty.get('offerList', [])
                        for offer in offer_list:
                            applies_to = offer.get('appliesTo', {})
                            record = {
                                'type': 'Warranty',
                                'identifier': offer.get('offerCode', ''),
                                'service_type': offer.get('offerCode', ''),
                                'service_desc': offer.get('offerDesc', ''),
                                'start_date': applies_to.get('startDate', ''),
                                'end_date': applies_to.get('endDate', ''),
                                'status': '有效' if applies_to.get('status') == 'V' else '失效'
                            }
                            result['warranty_records'].append(record)

                    # 提取维保记录 - 从contractList
                    contract_list = entitlement.get('contractList', [])
                    for contract in contract_list:
                        contract_id = contract.get('id', '')
                        project_name = contract.get('projectName', '')
                        offer_list = contract.get('offerList', [])
                        for offer in offer_list:
                            applies_to = offer.get('appliesTo', {})
                            record = {
                                'type': '合同',
                                'identifier': contract_id,
                                'service_type': offer.get('offerCode', ''),
                                'service_desc': offer.get('offerDesc', ''),
                                'start_date': applies_to.get('startDate', ''),
                                'end_date': applies_to.get('endDate', ''),
                                'status': '有效' if applies_to.get('status') == 'V' else '失效',
                                'project_name': project_name
                            }
                            result['warranty_records'].append(record)
                else:
                    result['query_status'] = '未找到设备信息'
            else:
                result['query_status'] = f"查询失败: {data.get('message', '未知错误')}"

        except Exception as e:
            result['query_status'] = f'解析错误: {str(e)}'

        return result
    
    def parse_html_response(self, html_content, serial_number):
        """
        解析HTML响应（备用方案）
        """
        result = {
            'serial_number': serial_number,
            'product_description': '',
            'product_line': '',
            'product_line_desc': '',
            'region': '',
            'warranty_records': [],
            'query_status': '成功',
            'query_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 这里可以添加HTML解析逻辑
        # 由于网站可能返回不同格式，这里先返回基本结构
        result['query_status'] = 'HTML响应需要进一步解析'
        return result
    
    def create_error_result(self, serial_number, error_msg):
        """
        创建错误结果
        """
        return {
            'serial_number': serial_number,
            'product_description': '',
            'product_line': '',
            'product_line_desc': '',
            'region': '',
            'warranty_records': [],
            'query_status': f'失败: {error_msg}',
            'query_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def batch_query(self, serial_numbers, delay=2):
        """
        批量查询维保信息
        
        Args:
            serial_numbers (list): 序列号列表
            delay (int): 查询间隔（秒）
            
        Returns:
            list: 查询结果列表
        """
        results = []
        total = len(serial_numbers)
        
        for i, serial_number in enumerate(serial_numbers, 1):
            print(f"进度: {i}/{total}")
            
            # 清理序列号（去除空格和特殊字符）
            clean_serial = str(serial_number).strip()
            if not clean_serial:
                continue
                
            result = self.query_warranty(clean_serial)
            results.append(result)
            
            # 添加延迟避免请求过于频繁
            if i < total:
                time.sleep(delay)
        
        return results

def read_device_info_from_excel(file_path):
    """
    从Excel文件读取设备信息
    """
    try:
        df = pd.read_excel(file_path, sheet_name='设备信息')
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def save_warranty_info_to_excel(device_df, warranty_results, output_file):
    """
    将维保信息保存到Excel文件
    """
    try:
        # 创建维保信息汇总表
        warranty_summary = []
        warranty_details = []
        
        for result in warranty_results:
            # 汇总信息
            summary = {
                '序列号': result['serial_number'],
                '产品描述': result['product_description'],
                '产品线': result['product_line'],
                '产品线描述': result['product_line_desc'],
                '区域': result['region'],
                '维保记录数': len(result['warranty_records']),
                '查询状态': result['query_status'],
                '查询时间': result['query_time']
            }
            
            # 获取最新的有效维保记录
            valid_warranties = [w for w in result['warranty_records'] if w['status'] not in ['失效', '取消']]
            if valid_warranties:
                latest = valid_warranties[0]  # 假设第一个是最新的
                summary.update({
                    '当前维保类型': latest['type'],
                    '当前服务类型': latest['service_type'],
                    '当前服务描述': latest['service_desc'],
                    '维保开始日期': latest['start_date'],
                    '维保结束日期': latest['end_date'],
                    '维保状态': latest['status']
                })
            else:
                summary.update({
                    '当前维保类型': '无有效维保',
                    '当前服务类型': '',
                    '当前服务描述': '',
                    '维保开始日期': '',
                    '维保结束日期': '',
                    '维保状态': '无'
                })
            
            warranty_summary.append(summary)
            
            # 详细记录
            for record in result['warranty_records']:
                detail = {
                    '序列号': result['serial_number'],
                    '产品描述': result['product_description'],
                    '维保类型': record['type'],
                    '标识符': record['identifier'],
                    '服务类型': record['service_type'],
                    '服务描述': record['service_desc'],
                    '开始日期': record['start_date'],
                    '结束日期': record['end_date'],
                    '状态': record['status'],
                    '项目名称': record.get('project_name', '')
                }
                warranty_details.append(detail)
        
        # 保存到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 原始设备信息
            if device_df is not None:
                device_df.to_excel(writer, sheet_name='设备信息', index=False)
            
            # 维保汇总信息
            if warranty_summary:
                summary_df = pd.DataFrame(warranty_summary)
                summary_df.to_excel(writer, sheet_name='维保汇总', index=False)
            
            # 维保详细记录
            if warranty_details:
                details_df = pd.DataFrame(warranty_details)
                details_df.to_excel(writer, sheet_name='维保详情', index=False)
            
            # 调整列宽
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"维保信息已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        return False

def main():
    """主函数"""
    print("H3C设备维保信息查询工具")
    print("=" * 50)
    
    # 查找最新的设备信息Excel文件
    import glob
    excel_files = glob.glob("设备信息统计_*.xlsx")
    if not excel_files:
        print("未找到设备信息Excel文件，请先运行extract_device_info.py")
        return
    
    # 使用最新的文件
    input_file = max(excel_files)
    print(f"读取设备信息文件: {input_file}")
    
    # 读取设备信息
    device_df = read_device_info_from_excel(input_file)
    if device_df is None:
        return
    
    print(f"找到 {len(device_df)} 条设备记录")
    
    # 提取序列号
    serial_numbers = []
    for _, row in device_df.iterrows():
        serial_str = str(row['序列号'])
        # 处理多个序列号的情况（用分号分隔）
        if ';' in serial_str:
            serials = [s.strip() for s in serial_str.split(';') if s.strip()]
            serial_numbers.extend(serials)
        else:
            if serial_str.strip():
                serial_numbers.append(serial_str.strip())
    
    print(f"需要查询 {len(serial_numbers)} 个序列号")
    
    # 创建查询器
    query_tool = H3CWarrantyQuery()
    
    # 批量查询
    print("\n开始查询维保信息...")
    warranty_results = query_tool.batch_query(serial_numbers, delay=3)
    
    # 保存结果
    output_file = f"设备维保信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    success = save_warranty_info_to_excel(device_df, warranty_results, output_file)
    
    if success:
        print(f"\n查询完成！")
        print(f"成功查询: {len([r for r in warranty_results if r['query_status'] == '成功'])} 条")
        print(f"查询失败: {len([r for r in warranty_results if r['query_status'] != '成功'])} 条")
        print(f"结果已保存到: {output_file}")
    else:
        print("保存结果失败")

if __name__ == "__main__":
    main()
