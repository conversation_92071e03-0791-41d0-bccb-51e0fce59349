# H3C维保查询批量输入序列号 - 油猴脚本使用说明

## 脚本功能

这个油猴脚本可以帮助你在H3C维保查询网站上批量输入序列号，支持一行一个SN的格式，大大提高查询效率。

## 安装步骤

### 1. 安装Tampermonkey（油猴）浏览器扩展

- **Chrome浏览器**: 在Chrome Web Store搜索"Tampermonkey"并安装
- **Firefox浏览器**: 在Firefox Add-ons搜索"Tampermonkey"并安装
- **Edge浏览器**: 在Microsoft Edge Add-ons搜索"Tampermonkey"并安装

### 2. 安装脚本

1. 打开Tampermonkey管理面板
2. 点击"创建新脚本"
3. 删除默认内容，复制粘贴以下任一脚本：
   - `h3c_simple_batch_input.user.js` - 简化版（推荐）
   - `h3c_batch_sn_input.user.js` - 完整版
4. 按 `Ctrl+S` 保存脚本

## 使用方法

### 1. 访问H3C维保查询网站
打开 https://es.h3c.com/entitlement/

### 2. 切换到批量查询页面
点击页面上的"批量查询"标签

### 3. 使用脚本
1. 页面右上角会出现一个蓝色的"📝 批量SN"按钮
2. 点击按钮打开批量输入面板
3. 在文本框中输入序列号，**每行一个**，例如：
   ```
   210231A0HMB119000227
   210231A0HMB142000024
   210231A0HMB115000111
   210231A0FSB127000010
   210231A76JB098000024
   ```
4. 点击"填入表单"按钮，脚本会自动将序列号填入网页的输入框
5. 点击网页的"查询"按钮开始查询

### 4. 其他功能
- **清空按钮**: 清空所有已填入的序列号
- **关闭面板**: 点击面板右上角的"×"按钮

## 注意事项

### 输入框限制
- H3C网站默认提供10个序列号输入框
- 如果你的序列号超过10个，需要手动点击网页上的"添加更多"按钮来增加输入框
- 脚本会提示你还有多少个序列号无法填入

### 序列号格式
- 每行一个序列号
- 自动过滤空行
- 支持复制粘贴Excel中的序列号列表

### 兼容性
- 支持Chrome、Firefox、Edge等主流浏览器
- 仅在H3C维保查询网站生效
- 需要在"批量查询"页面使用

## 脚本版本说明

### 简化版 (h3c_simple_batch_input.user.js)
- 文件更小，加载更快
- 基本的批量输入功能
- 推荐日常使用

### 完整版 (h3c_batch_sn_input.user.js)
- 功能更全面
- 更智能的输入框识别
- 支持拖拽面板
- 更详细的使用说明

## 常见问题

### Q: 脚本没有生效怎么办？
A: 
1. 确认Tampermonkey已正确安装并启用
2. 检查脚本是否已保存并启用
3. 刷新H3C维保查询页面
4. 确保在"批量查询"页面使用

### Q: 找不到"批量SN"按钮？
A: 
1. 检查页面右上角是否有蓝色按钮
2. 尝试刷新页面
3. 检查浏览器控制台是否有错误信息

### Q: 无法填入序列号？
A: 
1. 确保已切换到"批量查询"页面
2. 检查序列号格式（每行一个）
3. 尝试手动点击一个输入框，然后再使用脚本

### Q: 序列号超过10个怎么办？
A: 
1. 先填入前10个序列号
2. 手动点击网页上的"添加更多"按钮
3. 再次使用脚本填入剩余序列号

## 技术支持

如果遇到问题，可以：
1. 检查浏览器控制台的错误信息
2. 确认H3C网站结构是否有变化
3. 尝试重新安装脚本

## 更新日志

- v1.1: 改进输入框识别算法，增加简化版本
- v1.0: 初始版本，基本批量输入功能
