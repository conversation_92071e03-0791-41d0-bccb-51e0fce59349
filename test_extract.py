#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备信息提取
"""

import os
import re

def extract_device_info_from_file(file_path):
    """
    从单个txt文件中提取设备制造信息
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 初始化设备信息字典
        device_info = {
            'file_name': os.path.basename(file_path),
            'device_name': '',
            'serial_number': '',
            'mac_address': '',
            'manufacturing_date': '',
            'vendor_name': ''
        }
        
        # 1. 提取sysname作为设备名称
        sysname_pattern = r'sysname\s+(.+)'
        sysname_match = re.search(sysname_pattern, content)
        if sysname_match:
            device_info['device_name'] = sysname_match.group(1).strip()
        
        # 2. 查找设备制造信息部分
        # 尝试多种格式的设备制造信息
        manuinfo_patterns = [
            # 格式1: Subslot 0
            r'===============display device manuinfo===============.*?Subslot 0\s*\n(.*?)(?=\n\s*\n|===============)',
            # 格式2: Slot X CPU Y:
            r'===============display device manuinfo===============(.*?)(?=\n\s*\n|===============)',
        ]
        
        device_info_section = None
        for pattern in manuinfo_patterns:
            manuinfo_match = re.search(pattern, content, re.DOTALL)
            if manuinfo_match:
                device_info_section = manuinfo_match.group(1)
                break
        
        if not device_info_section:
            print(f"未在文件 {file_path} 中找到设备制造信息")
            return device_info if device_info['device_name'] else None
        
        print(f"找到设备制造信息部分:")
        print(device_info_section[:500])  # 打印前500个字符
        print("-" * 50)
        
        # 提取制造信息，支持多种格式
        manu_patterns = {
            'serial_number': [
                r'DEVICE_SERIAL_NUMBER\s*:\s*(.+)',
                r'DEVICE_SERIAL_NUMBER:(.+)'
            ],
            'mac_address': [
                r'MAC_ADDRESS\s*:\s*(.+)',
                r'MAC_ADDRESS:(.+)'
            ],
            'manufacturing_date': [
                r'MANUFACTURING_DATE\s*:\s*(.+)',
                r'MANUFACTURING_DATE:(.+)'
            ],
            'vendor_name': [
                r'VENDOR_NAME\s*:\s*(.+)',
                r'VENDOR_NAME:(.+)'
            ]
        }
        
        # 如果有多个设备，提取第一个设备的信息
        for field, patterns in manu_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, device_info_section)
                if match:
                    device_info[field] = match.group(1).strip()
                    break
        
        # 如果有多个序列号，将它们合并
        all_serial_numbers = []
        for pattern in manu_patterns['serial_number']:
            matches = re.findall(pattern, device_info_section)
            for match in matches:
                serial = match.strip()
                if serial and serial not in all_serial_numbers:
                    all_serial_numbers.append(serial)
        
        if all_serial_numbers:
            device_info['serial_number'] = '; '.join(all_serial_numbers)
        
        return device_info
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None

# 测试第一个文件
test_file = "20240830/*************-Default--20240830-093435.txt"
if os.path.exists(test_file):
    print(f"测试文件: {test_file}")
    result = extract_device_info_from_file(test_file)
    print("提取结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
else:
    print(f"测试文件不存在: {test_file}")
