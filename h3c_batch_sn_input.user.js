// ==UserScript==
// @name         H3C维保查询批量输入序列号
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  在H3C维保查询页面批量输入序列号，支持一行一个SN的格式
// <AUTHOR>
// @match        https://es.h3c.com/entitlement/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }

    // 创建批量输入界面
    function createBatchInputUI() {
        // 创建浮动面板
        const panel = document.createElement('div');
        panel.id = 'batch-sn-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: Arial, sans-serif;
            display: none;
        `;

        panel.innerHTML = `
            <div style="background: #007bff; color: white; padding: 10px; border-radius: 6px 6px 0 0; cursor: move;">
                <strong>批量输入序列号</strong>
                <button id="close-panel" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
            </div>
            <div style="padding: 15px;">
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">序列号列表（一行一个）：</label>
                    <textarea id="sn-input" placeholder="请输入序列号，每行一个&#10;例如：&#10;210231A0HMB119000227&#10;210231A0HMB142000024&#10;210231A0HMB115000111" 
                        style="width: 100%; height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 8px; font-family: monospace; font-size: 12px; resize: vertical;"></textarea>
                </div>
                <div style="margin-bottom: 10px;">
                    <button id="fill-batch" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">填入表单</button>
                    <button id="clear-all" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">清空表单</button>
                    <button id="add-more" style="background: #ffc107; color: black; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">添加更多</button>
                </div>
                <div style="font-size: 12px; color: #666;">
                    <p>使用说明：</p>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>在文本框中输入序列号，每行一个</li>
                        <li>点击"填入表单"自动填入网页表单</li>
                        <li>如果序列号超过10个，会自动点击"添加更多"</li>
                        <li>支持批量查询页面的所有输入框</li>
                    </ul>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // 添加拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        const header = panel.querySelector('div');
        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragOffset.x = e.clientX - panel.offsetLeft;
            dragOffset.y = e.clientY - panel.offsetTop;
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                panel.style.left = (e.clientX - dragOffset.x) + 'px';
                panel.style.top = (e.clientY - dragOffset.y) + 'px';
                panel.style.right = 'auto';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // 关闭面板
        document.getElementById('close-panel').addEventListener('click', () => {
            panel.style.display = 'none';
        });

        return panel;
    }

    // 创建触发按钮
    function createTriggerButton() {
        const button = document.createElement('button');
        button.id = 'batch-sn-trigger';
        button.innerHTML = '📝 批量输入SN';
        button.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            z-index: 9999;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        button.addEventListener('click', () => {
            const panel = document.getElementById('batch-sn-panel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        });

        document.body.appendChild(button);
        return button;
    }

    // 获取所有序列号输入框
    function getSerialNumberInputs() {
        const inputs = [];

        // 方法1: 通过placeholder查找
        let snInputs = document.querySelectorAll('input[placeholder*="序列号"]');
        snInputs.forEach(input => {
            if (!input.placeholder.includes('产品号')) {
                inputs.push(input);
            }
        });

        // 方法2: 通过ID查找（H3C网站的输入框ID模式）
        if (inputs.length === 0) {
            // 查找批量查询页面的输入框
            const batchInputs = document.querySelectorAll('#query_form input[type="text"]');
            batchInputs.forEach((input) => {
                // 在批量查询页面，序列号输入框通常是每组的第一个
                const parent = input.closest('div');
                if (parent && parent.textContent.includes('序列号')) {
                    inputs.push(input);
                }
            });
        }

        // 方法3: 通过表单结构查找
        if (inputs.length === 0) {
            const allInputs = document.querySelectorAll('input[type="text"]');
            allInputs.forEach(input => {
                const label = input.previousElementSibling;
                if (label && label.textContent && label.textContent.includes('序列号')) {
                    inputs.push(input);
                }
            });
        }

        // 方法4: 通过特定的ID模式查找（sn_0, sn_1等）
        if (inputs.length === 0) {
            for (let i = 0; i < 20; i++) {
                const input = document.getElementById(`sn_${i}`) || document.querySelector(`input[id*="sn_${i}"]`);
                if (input) {
                    inputs.push(input);
                }
            }
        }

        console.log(`找到 ${inputs.length} 个序列号输入框`);
        return inputs;
    }

    // 点击添加更多按钮
    function clickAddMore() {
        // 方法1: 直接查找"添加更多"按钮
        const buttons = document.querySelectorAll('button, input[type="button"]');
        for (let btn of buttons) {
            const text = btn.textContent || btn.value || '';
            if (text.includes('添加更多') || text.includes('添加') || text.includes('Add More') || text.includes('Add')) {
                console.log('找到添加更多按钮:', text);
                btn.click();
                return true;
            }
        }

        // 方法2: 通过onclick属性查找
        const clickableElements = document.querySelectorAll('[onclick*="add"], [onclick*="Add"], [onclick*="more"]');
        if (clickableElements.length > 0) {
            console.log('通过onclick找到添加按钮');
            clickableElements[0].click();
            return true;
        }

        // 方法3: 查找可能的JavaScript函数调用
        const scripts = document.querySelectorAll('script');
        for (let script of scripts) {
            if (script.textContent.includes('addMore') || script.textContent.includes('add_more')) {
                // 尝试执行添加更多的函数
                try {
                    if (typeof window.addMore === 'function') {
                        window.addMore();
                        return true;
                    }
                    if (typeof window.add_more === 'function') {
                        window.add_more();
                        return true;
                    }
                } catch (e) {
                    console.log('执行添加更多函数失败:', e);
                }
            }
        }

        console.log('未找到添加更多按钮');
        return false;
    }

    // 填入序列号
    function fillSerialNumbers(serialNumbers) {
        const inputs = getSerialNumberInputs();
        let currentIndex = 0;

        // 清空现有输入
        inputs.forEach(input => {
            input.value = '';
            // 触发change事件
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        });

        // 填入序列号
        function fillNext() {
            const availableInputs = getSerialNumberInputs();
            
            for (let i = 0; i < availableInputs.length && currentIndex < serialNumbers.length; i++) {
                if (availableInputs[i] && !availableInputs[i].value) {
                    availableInputs[i].value = serialNumbers[currentIndex];
                    availableInputs[i].dispatchEvent(new Event('input', { bubbles: true }));
                    availableInputs[i].dispatchEvent(new Event('change', { bubbles: true }));
                    currentIndex++;
                }
            }

            // 如果还有序列号需要填入，尝试添加更多输入框
            if (currentIndex < serialNumbers.length) {
                if (clickAddMore()) {
                    // 等待新输入框加载后继续填入
                    setTimeout(fillNext, 500);
                } else {
                    alert(`已填入 ${currentIndex} 个序列号，剩余 ${serialNumbers.length - currentIndex} 个无法填入（页面输入框不足）`);
                }
            } else {
                alert(`成功填入 ${currentIndex} 个序列号！`);
            }
        }

        fillNext();
    }

    // 清空所有输入框
    function clearAllInputs() {
        const inputs = getSerialNumberInputs();
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        });
        alert('已清空所有序列号输入框！');
    }

    // 初始化
    async function init() {
        try {
            // 等待页面加载
            await waitForElement('body');
            
            // 创建UI
            const panel = createBatchInputUI();
            const trigger = createTriggerButton();

            // 绑定事件
            document.getElementById('fill-batch').addEventListener('click', () => {
                const textarea = document.getElementById('sn-input');
                const text = textarea.value.trim();
                
                if (!text) {
                    alert('请输入序列号！');
                    return;
                }

                const serialNumbers = text.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);

                if (serialNumbers.length === 0) {
                    alert('没有找到有效的序列号！');
                    return;
                }

                fillSerialNumbers(serialNumbers);
            });

            document.getElementById('clear-all').addEventListener('click', clearAllInputs);

            document.getElementById('add-more').addEventListener('click', () => {
                if (clickAddMore()) {
                    alert('已点击添加更多按钮！');
                } else {
                    alert('未找到"添加更多"按钮！');
                }
            });

            console.log('H3C批量输入序列号脚本已加载！');

        } catch (error) {
            console.error('脚本初始化失败:', error);
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
