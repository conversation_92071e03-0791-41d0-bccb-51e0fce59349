#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备信息提取脚本
从20240830文件夹中的txt文件提取设备制造信息并生成Excel表格
"""

import os
import re
import pandas as pd
from datetime import datetime

def extract_device_info_from_file(file_path):
    """
    从单个txt文件中提取设备制造信息

    Args:
        file_path (str): txt文件路径

    Returns:
        dict: 包含设备信息的字典，如果未找到则返回None
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # 初始化设备信息字典
        device_info = {
            'file_name': os.path.basename(file_path),
            'device_name': '',
            'serial_number': '',
            'mac_address': '',
            'manufacturing_date': '',
            'vendor_name': ''
        }

        # 1. 提取sysname作为设备名称
        sysname_pattern = r'sysname\s+(.+)'
        sysname_match = re.search(sysname_pattern, content)
        if sysname_match:
            device_info['device_name'] = sysname_match.group(1).strip()

        # 2. 查找设备制造信息部分
        # 尝试多种格式的设备制造信息
        manuinfo_patterns = [
            # 格式1: Subslot 0
            r'===============display device manuinfo===============.*?Subslot 0\s*\n(.*?)(?=\n\s*\n|===============)',
            # 格式2: Slot X CPU Y:
            r'===============display device manuinfo===============(.*?)(?=\n\s*\n|===============)',
        ]

        device_info_section = None
        for pattern in manuinfo_patterns:
            manuinfo_match = re.search(pattern, content, re.DOTALL)
            if manuinfo_match:
                device_info_section = manuinfo_match.group(1)
                break

        if not device_info_section:
            print(f"未在文件 {file_path} 中找到设备制造信息")
            # 如果找到了sysname但没有找到制造信息，仍然返回部分信息
            if device_info['device_name']:
                return device_info
            return None

        # 提取制造信息，支持多种格式
        # 格式1: DEVICE_SERIAL_NUMBER  : xxxxx
        # 格式2: DEVICE_SERIAL_NUMBER:xxxxx
        manu_patterns = {
            'serial_number': [
                r'DEVICE_SERIAL_NUMBER\s*:\s*(.+)',
                r'DEVICE_SERIAL_NUMBER:(.+)'
            ],
            'mac_address': [
                r'MAC_ADDRESS\s*:\s*(.+)',
                r'MAC_ADDRESS:(.+)'
            ],
            'manufacturing_date': [
                r'MANUFACTURING_DATE\s*:\s*(.+)',
                r'MANUFACTURING_DATE:(.+)'
            ],
            'vendor_name': [
                r'VENDOR_NAME\s*:\s*(.+)',
                r'VENDOR_NAME:(.+)'
            ]
        }

        # 如果有多个设备，提取第一个设备的信息
        for field, patterns in manu_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, device_info_section)
                if match:
                    device_info[field] = match.group(1).strip()
                    break

        # 如果有多个序列号，将它们合并
        all_serial_numbers = []
        for pattern in manu_patterns['serial_number']:
            matches = re.findall(pattern, device_info_section)
            for match in matches:
                serial = match.strip()
                if serial and serial not in all_serial_numbers:
                    all_serial_numbers.append(serial)

        if all_serial_numbers:
            device_info['serial_number'] = '; '.join(all_serial_numbers)

        return device_info

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None

def extract_all_device_info(folder_path):
    """
    从文件夹中的所有txt文件提取设备信息
    
    Args:
        folder_path (str): 包含txt文件的文件夹路径
        
    Returns:
        list: 包含所有设备信息的列表
    """
    device_info_list = []
    
    # 获取文件夹中的所有txt文件
    txt_files = [f for f in os.listdir(folder_path) if f.endswith('.txt')]
    txt_files.sort()  # 按文件名排序
    
    print(f"找到 {len(txt_files)} 个txt文件")
    
    for i, txt_file in enumerate(txt_files, 1):
        file_path = os.path.join(folder_path, txt_file)
        print(f"正在处理 ({i}/{len(txt_files)}): {txt_file}")
        
        device_info = extract_device_info_from_file(file_path)
        if device_info:
            device_info_list.append(device_info)
    
    return device_info_list

def save_to_excel(device_info_list, output_file):
    """
    将设备信息保存到Excel文件
    
    Args:
        device_info_list (list): 设备信息列表
        output_file (str): 输出Excel文件路径
    """
    if not device_info_list:
        print("没有找到任何设备信息")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(device_info_list)
    
    # 重命名列为中文
    column_mapping = {
        'file_name': '文件名',
        'device_name': '设备名称',
        'serial_number': '序列号',
        'mac_address': 'MAC地址',
        'manufacturing_date': '制造日期',
        'vendor_name': '厂商名称'
    }
    
    df = df.rename(columns=column_mapping)
    
    # 重新排列列的顺序
    df = df[['文件名', '设备名称', '序列号', 'MAC地址', '制造日期', '厂商名称']]
    
    # 保存到Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='设备信息', index=False)
        
        # 获取工作表对象以调整列宽
        worksheet = writer.sheets['设备信息']
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"设备信息已保存到: {output_file}")
    print(f"共提取了 {len(device_info_list)} 条设备信息")

def main():
    """主函数"""
    folder_path = "20240830"
    output_file = f"设备信息统计_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    print("开始提取设备信息...")
    print(f"源文件夹: {folder_path}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        return
    
    # 提取设备信息
    device_info_list = extract_all_device_info(folder_path)
    
    # 保存到Excel
    if device_info_list:
        save_to_excel(device_info_list, output_file)
        
        # 显示统计信息
        print("\n" + "=" * 50)
        print("提取完成！统计信息:")
        print(f"处理文件数: {len(os.listdir(folder_path))}")
        print(f"成功提取: {len(device_info_list)} 条记录")
        
        # 显示前几条记录作为预览
        if device_info_list:
            print("\n前3条记录预览:")
            df_preview = pd.DataFrame(device_info_list[:3])
            df_preview = df_preview.rename(columns={
                'file_name': '文件名',
                'device_name': '设备名称', 
                'serial_number': '序列号',
                'mac_address': 'MAC地址',
                'manufacturing_date': '制造日期',
                'vendor_name': '厂商名称'
            })
            print(df_preview.to_string(index=False))
    else:
        print("未找到任何设备信息")

if __name__ == "__main__":
    main()
