// ==UserScript==
// @name         H3C维保查询批量输入SN（简化版）
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  在H3C维保查询页面批量输入序列号，一行一个SN
// <AUTHOR>
// @match        https://es.h3c.com/entitlement/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 创建浮动输入面板
    function createInputPanel() {
        const panel = document.createElement('div');
        panel.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; width: 300px; background: white; border: 2px solid #007bff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000; font-family: Arial, sans-serif;">
                <div style="background: #007bff; color: white; padding: 10px; border-radius: 6px 6px 0 0;">
                    <strong>批量输入SN</strong>
                    <button onclick="this.closest('div').parentElement.style.display='none'" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
                </div>
                <div style="padding: 15px;">
                    <textarea id="batch-sn-input" placeholder="请输入序列号，每行一个：&#10;210231A0HMB119000227&#10;210231A0HMB142000024&#10;210231A0HMB115000111" 
                        style="width: 100%; height: 120px; border: 1px solid #ddd; border-radius: 4px; padding: 8px; font-family: monospace; font-size: 12px; resize: vertical; box-sizing: border-box;"></textarea>
                    <div style="margin-top: 10px;">
                        <button onclick="fillBatchSN()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">填入表单</button>
                        <button onclick="clearAllSN()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">清空</button>
                    </div>
                    <div style="font-size: 11px; color: #666; margin-top: 8px;">
                        提示：先切换到"批量查询"标签，然后输入序列号点击填入
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(panel);
    }

    // 创建触发按钮
    function createTriggerButton() {
        const button = document.createElement('button');
        button.innerHTML = '📝 批量SN';
        button.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            z-index: 9999;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        
        button.onclick = function() {
            const panel = document.querySelector('[style*="position: fixed"][style*="top: 20px"]');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        };
        
        document.body.appendChild(button);
    }

    // 获取序列号输入框
    window.getSerialInputs = function() {
        const inputs = [];
        
        // 查找所有文本输入框
        const allInputs = document.querySelectorAll('input[type="text"]');
        
        allInputs.forEach(input => {
            // 检查是否是序列号输入框
            const placeholder = input.placeholder || '';
            const prevText = input.previousElementSibling ? input.previousElementSibling.textContent : '';
            const parentText = input.parentElement ? input.parentElement.textContent : '';
            
            if (placeholder.includes('序列号') || 
                prevText.includes('序列号') || 
                parentText.includes('序列号')) {
                // 排除产品号输入框
                if (!placeholder.includes('产品号') && 
                    !prevText.includes('产品号') && 
                    !parentText.includes('产品号')) {
                    inputs.push(input);
                }
            }
        });
        
        console.log('找到序列号输入框:', inputs.length);
        return inputs;
    };

    // 填入批量序列号
    window.fillBatchSN = function() {
        const textarea = document.getElementById('batch-sn-input');
        if (!textarea) return;
        
        const text = textarea.value.trim();
        if (!text) {
            alert('请输入序列号！');
            return;
        }
        
        const serialNumbers = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
            
        if (serialNumbers.length === 0) {
            alert('没有找到有效的序列号！');
            return;
        }
        
        const inputs = window.getSerialInputs();
        if (inputs.length === 0) {
            alert('未找到序列号输入框！请确保已切换到"批量查询"页面。');
            return;
        }
        
        // 清空现有输入
        inputs.forEach(input => {
            input.value = '';
        });
        
        // 填入序列号
        let filled = 0;
        for (let i = 0; i < Math.min(serialNumbers.length, inputs.length); i++) {
            inputs[i].value = serialNumbers[i];
            inputs[i].dispatchEvent(new Event('input', { bubbles: true }));
            inputs[i].dispatchEvent(new Event('change', { bubbles: true }));
            filled++;
        }
        
        if (serialNumbers.length > inputs.length) {
            alert(`已填入 ${filled} 个序列号，还有 ${serialNumbers.length - filled} 个序列号无法填入（输入框不足）。\n\n请点击"添加更多"按钮增加输入框。`);
        } else {
            alert(`成功填入 ${filled} 个序列号！`);
        }
    };

    // 清空所有序列号输入框
    window.clearAllSN = function() {
        const inputs = window.getSerialInputs();
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        });
        alert(`已清空 ${inputs.length} 个序列号输入框！`);
    };

    // 页面加载完成后初始化
    function init() {
        // 等待页面加载
        setTimeout(() => {
            createInputPanel();
            createTriggerButton();
            console.log('H3C批量输入SN脚本已加载！');
        }, 1000);
    }

    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
