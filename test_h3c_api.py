#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试H3C维保查询API
"""

import requests
import json

def test_h3c_api():
    """测试H3C API"""
    url = "https://es.h3c.com/entitlement/query"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://es.h3c.com/entitlement/',
        'Origin': 'https://es.h3c.com'
    }
    
    params = {
        'serialNumber': '210231A0HMB119000227',
        'productNumber': '',
        'language': 'CN',
        'captchaVerification': ''
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容类型: {response.headers.get('content-type', 'unknown')}")
        print(f"响应长度: {len(response.text)}")
        print("\n响应内容:")
        print(response.text[:2000])  # 打印前2000个字符
        
        # 尝试解析为JSON
        try:
            data = response.json()
            print("\n解析为JSON成功:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        except:
            print("\n无法解析为JSON，可能是HTML响应")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_h3c_api()
